using ToroEhr.Shared;

namespace ToroEhr.Infrastructure.Mediatr.Auth;

public record UserRequestSession(
    string UserId,
    string SessionId,
    string Email,
    string FirstName,
    string LastName,
    string SelectedUserRole,
    string? EmployeeId,
    string? PatientId,
    string? SelectedOrganizationId,
    string? OrganizationName,
    string? OrganizationType,
    string? SelectedLocationId,
    string? LocationName,
    IEnumerable<string>? SelectedLocationEmployeeRoles)
{
    public string FullName => $"{FirstName} {LastName}";
    
    public bool IsPatient => PatientId.IsNotNullOrWhiteSpace();
    
    public bool IsEmployee => EmployeeId.IsNotNullOrWhiteSpace();
};