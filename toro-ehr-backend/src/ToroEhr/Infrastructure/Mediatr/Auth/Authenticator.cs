using System.Security.Authentication;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Shared;

namespace ToroEhr.Infrastructure.Mediatr.Auth;

internal class Authenticator
{
    private readonly IHttpContextAccessor _ctx;
    private readonly IDocumentStore _store;
    private readonly Lazy<UserRequestSession> _user;

    public Authenticator(IHttpContextAccessor ctx, IDocumentStore documentStore)
    {
        _ctx = ctx;
        _store = documentStore;

        _user ??= new Lazy<UserRequestSession>(AuthenticateUser);
    }

    public UserRequestSession User => _user.Value;

    private UserRequestSession AuthenticateUser()
    {
        string? bearerAccessToken = _ctx.HttpContext?.Request.Headers.Authorization.ToString();

        // Check if coming from a background job
        if (bearerAccessToken == null && _ctx.HttpContext == null)
        {
            //return new UserRequestSession(string.Empty, string.Empty, "ToroHealthBot", string.Empty, string.Empty, string.Empty, 
            //    string.Empty, string.Empty, string.Empty, string.Empty, [ UserRole.SuperAdmin ], null);
            return new UserRequestSession(string.Empty, string.Empty, string.Empty, "ToroHealthBot", string.Empty, UserRole.SuperAdmin, string.Empty, string.Empty,
                string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, null);

        }

        AuthenticationGuard.AgainstNullOrEmpty(bearerAccessToken);

        string accessToken = bearerAccessToken!.Replace("Bearer", "").Trim();

        if (accessToken.IsNullOrWhiteSpace())
        {
            throw new AuthenticationException();
        }

        using IDocumentSession session = _store.OpenSession();

        var userSession = session.Query<UserSession>()
            .Include(x => x.UserId)
            .FirstOrDefault(x => x.AccessToken == accessToken);
        AuthenticationGuard.AgainstNull(userSession);

        User user = session.Load<User>(userSession!.UserId);
        AuthenticationGuard.AgainstNull(user);

        LocationEmployee? employeeLocation = null;
        Organization? organization = null;
        Location? location = null;
        if (userSession.UserRole == UserRole.Employee)
        {
            employeeLocation = session
            .Query<LocationEmployee>()
            .Include(op => op.OrganizationId)
            .Include(op => op.LocationId)
            .FirstOrDefault(op =>
                    op.EmployeeId == user.EmployeeId && op.LocationId == userSession.LocationId);
            AuthenticationGuard.AgainstNull(employeeLocation);
            organization = session.Load<Organization>(employeeLocation!.OrganizationId);
            location = session.Load<Location>(employeeLocation!.LocationId);
        }
        //if (user.EmployeeId != null && userSession.OrganizationId != null)
        //{
        //    employeeLocation = session
        //        .Query<LocationEmployee>()
        //        .Include(op => op.OrganizationId)
        //        .Include(op => op.LocationId)
        //        .FirstOrDefault(op =>
        //            op.EmployeeId == user.EmployeeId && op.OrganizationId == userSession.OrganizationId);
        //    AuthenticationGuard.AgainstNull(employeeLocation);
        //    organization = session.Load<Organization>(employeeLocation!.OrganizationId);
        //    location = session.Load<Location>(employeeLocation!.LocationId);
        //}

        //return new UserRequestSession(user.Id, user.PrimaryEmail, user.FirstName, user.LastName, user.EmployeeId,
        //    user.PatientId, userSession.OrganizationId, organization?.Name, location?.Id, location?.Name, user.UserRoles,
        //    employeeLocation?.EmployeeRoles ?? Enumerable.Empty<string>());
        return new UserRequestSession(user.Id, userSession.Id, user.PrimaryEmail, user.FirstName, user.LastName, userSession.UserRole, user.EmployeeId,
            user.PatientId, userSession.OrganizationId, organization?.Name, organization?.Type, userSession?.LocationId, location?.Name,
            employeeLocation?.EmployeeRoles ?? Enumerable.Empty<string>());
    }
}