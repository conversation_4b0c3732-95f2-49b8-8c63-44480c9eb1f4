using System.Security.Authentication;
using Sentry;
using ToroEhr.Infrastructure.Exceptions;

namespace ToroEhr.Infrastructure.ErrorHandling;

/// <summary>
/// filters out intentional business exceptions from being sent to Sentry
/// while allowing genuine unexpected errors to be captured
/// </summary>
public static class SentryExceptionFilter
{
    /// <summary>
    /// determines if an exception should be sent to Sentry
    /// returns false for intentional business exceptions that are handled by GlobalExceptionHandler
    /// </summary>
    public static bool ShouldCaptureException(Exception exception)
    {
        return exception switch
        {
            // exclude intentional business exceptions - these are expected and handled
            ValidationException => false,
            PaymentException => false,
            NotFoundException => false,
            AuthenticationException => false,
            UnauthorizedAccessException => false,
            
            // capture all other exceptions as they represent unexpected errors
            _ => true
        };
    }

    /// <summary>
    /// configures Sentry to use the exception filter
    /// </summary>
    public static void ConfigureExceptionFilter(SentryOptions options)
    {
        options.SetBeforeSend((sentryEvent, hint) =>
        {
            // check if there's an exception in the event
            if (sentryEvent.Exception != null)
            {
                // filter out business exceptions
                if (!ShouldCaptureException(sentryEvent.Exception))
                {
                    return null; // don't send to Sentry
                }
            }

            return sentryEvent; // send to Sentry
        });
    }
}
