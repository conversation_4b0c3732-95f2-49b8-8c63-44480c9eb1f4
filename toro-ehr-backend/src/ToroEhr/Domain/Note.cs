using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class Note : Entity
{
    [JsonConstructor]
    private Note(string name, string classification, string encounterId, string patientId, string practitionerId,
        List<NoteField> fields, string cptCode, List<string> icdCodes, DateTime signedAt)
    {
        NoteId = Id;
        Version = 1;
        IsLatest = true;
        Name = name;
        Classification = classification;
        EncounterId = encounterId;
        PatientId = patientId;
        PractitionerId = practitionerId;
        Fields = fields;
        CptCode = cptCode;
        IcdCodes = icdCodes ?? new List<string>();
        SignedAt = signedAt;
    }

    public string NoteId { get; private set; }

    public int Version { get; private set; }

    public bool IsLatest { get; private set; }

    public string Name { get; private set; }

    public string Classification { get; private set; }

    public string EncounterId { get; private set; }

    public string PatientId { get; private set; }

    public string PractitionerId { get; private set; }

    public List<NoteField> Fields { get; private set; }

    public string CptCode { get; private set; }

    public List<string> IcdCodes { get; private set; }

    public DateTime SignedAt { get; private set; }

    public bool IsCompleted { get; private set; }

    public static Note Create(string name, string classification, string encounterId, string patientId,
        string practitionerId,
        List<NoteField> fields, string cptCode, List<string> icdCodes, DateTime signedAt) =>
        new(name, classification, encounterId, patientId, practitionerId, fields, cptCode, icdCodes, signedAt);

    public void MarkAsNotLatest()
    {
        IsLatest = false;
    }

    public void MarkCompleted()
    {
        IsCompleted = true;
    }

    public static Note CreateNewVersion(Note previousNote, List<NoteField> newFields, string cptCode, List<string> icdCodes,
        DateTime signedAt)
    {
        return new Note(
            previousNote.Name,
            previousNote.Classification,
            previousNote.EncounterId,
            previousNote.PatientId,
            previousNote.PractitionerId,
            newFields,
            cptCode,
            icdCodes,
            signedAt
        )
        {
            NoteId = previousNote.NoteId,
            Version = previousNote.Version + 1,
            IsLatest = true
        };
    }
}

public record NoteField(string Name, string Value);