using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;

namespace ToroEhr.Features.Patient;

public sealed record SetPatientPersonalInfoCommand(
    string PatientId,
    string FirstName,
    string LastName,
    string? MiddleName,
    string? Suffix,
    string? PreferredName,
    string? PreviousFirstName,
    string? PreviousLastName,
    string? PreferredLanguage,
    DateTime Birthday,
    string BirthSex,
    string? GenderIdentity,
    string? SexualOrientation,
    string? Race,
    string? Ethnicity,
    string? TribalAffiliation,
    decimal? HeightInCm,
    decimal? WeightInKg,
    IEnumerable<PatientDocument> NewDocuments,
    IEnumerable<string> DeleteDocumentIds) : AuthRequest<Unit>;

public record PatientDocument(IEnumerable<IFormFile> DocumentFiles, string DocumentType);

internal sealed class SetPatientPersonalInfoAuth : IAuth<SetPatientPersonalInfoCommand, Unit>
{
    public SetPatientPersonalInfoAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

public sealed class SetPatientPersonalInfoCommandValidator : AbstractValidator<SetPatientPersonalInfoCommand>
{
    public SetPatientPersonalInfoCommandValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.FirstName).NotEmpty();
        RuleFor(x => x.LastName).NotEmpty();
        RuleFor(x => x.Birthday).NotEmpty();
        RuleFor(x => x.BirthSex).NotEmpty();

        // validate that only one document per type is allowed in the request
        RuleFor(x => x.NewDocuments)
            .Must(documents => documents.GroupBy(d => d.DocumentType).All(g => g.Count() == 1))
            .WithMessage("Only one document per type is allowed. Duplicate document types found in request.");

        RuleForEach(x => x.NewDocuments)
            .ChildRules(document =>
            {
                document.RuleFor(d => d.DocumentType).NotEmpty().WithMessage("Document type is required.");
                document.RuleFor(d => d.DocumentFiles).NotEmpty().WithMessage("At least one document file is required.");
            });
    }
}

internal sealed class SetPatientPersonalInfoHandler : IRequestHandler<SetPatientPersonalInfoCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly S3FileService _fileService;

    public SetPatientPersonalInfoHandler(IDocumentStore store, S3FileService fileService)
    {
        _store = store;
        _fileService = fileService;
    }

    public async Task<Unit> Handle(SetPatientPersonalInfoCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var patient = await session.LoadAsync<Domain.Patient>(command.PatientId, cancellationToken);
        Guard.AgainstNotFound(patient, PatientErrors.NotFoundById(command.PatientId));

        patient.SetPersonalInfo(command);

        // handle previous names if provided
        if (!string.IsNullOrWhiteSpace(command.PreviousFirstName))
        {
            patient.SetPreviousFirstName(command.PreviousFirstName, command.Timestamp);
        }

        if (!string.IsNullOrWhiteSpace(command.PreviousLastName))
        {
            patient.SetPreviousLastName(command.PreviousLastName, command.Timestamp);
        }

        // validate that document types don't already exist for the patient
        if (command.NewDocuments.Any())
        {
            var existingDocumentTypes = patient.Documents.Select(d => d.Type).ToHashSet(StringComparer.OrdinalIgnoreCase);
            var duplicateTypes = command.NewDocuments
                .Where(d => existingDocumentTypes.Contains(d.DocumentType))
                .Select(d => d.DocumentType)
                .ToList();

            if (duplicateTypes.Any())
            {
                throw new ValidationException($"Document type(s) already exist for this patient: {string.Join(", ", duplicateTypes)}. Only one document per type is allowed.");
            }
        }

        foreach (var document in command.NewDocuments)
        {
            string documentId = Utils.GenerateRandomId();
            string folderPath = $"patients/{patient.Id}/documents/{documentId}";

            List<string> filePaths = new();

            foreach (var documentFile in document.DocumentFiles)
            {
                await using var stream = documentFile.OpenReadStream();
                string filePath = $"{folderPath}/{documentFile.FileName}";
                await _fileService.UploadFile(stream, documentFile.ContentType, Config.S3.AppFilesBucketName, filePath);
                filePaths.Add(filePath);
            }

            patient.AddDocument(documentId, document.DocumentType, filePaths, command.Timestamp);
        }

        // TODO djordje: Check what is this
        /*if (command.DeleteDocumentIds.Any())
        {
            await _fileService.DeleteFiles(command.DeleteDocumentIds, Config.S3.AppFilesBucketName);
            patient.DeleteDocuments(command.DeleteDocumentIds);
        }*/

        await session.StoreAsync(patient, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}