using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using System.Threading;
using ToroEhr.Indexes;
using ToroEhr.Domain;
using Raven.Client.Documents.Linq;

namespace ToroEhr.Features.Patient;

public sealed record ListPatientCareQuery(string PatientId, string EncounterId) : AuthRequest<PatientCareResponse>;

public sealed record PatientCareResponse(
    List<CareTeamResponse> CareTeamItems, List<CarePlanResponse> CarePlanItems, List<CareEventsResponse> CareEvents);

public sealed record CareTeamResponse(
    string Spetialty, string Color, string FullName, string Phone, DateTimeOffset? LastSeen, string? Order, string? OrderResult);

public sealed record CarePlanResponse( string Id,
    DateTimeOffset Date, bool IsConfirmed, string Practitioner, string Title);

public sealed record CareEventsResponse(
    DateTimeOffset Date, 
    string Type,
    EventPractitionerResponse? Practitioner,
    ProblemEventResponse? Problem,
    MedicationEventResponse? Medication,
    LabEventResponse? Lab);

public sealed record EventPractitionerResponse(
    string Id,
    string Name,
    string Color,
    string SpecialtyClassification,
    string Phone);

public sealed record ProblemEventResponse(
    string ConditionDescription,
    string ClinicalStatus,
    string VerificationStatus,
    DateOnly DiagnosisDate,
    DateOnly? Abatement);


public sealed record MedicationEventResponse(
    string? Name,
    string? Status,
    string Frequency,
    string? CustomFrequency,
    string Duration,
    bool? Prn,
    string? PrnReason,
    string? MedRequest,
    string? EncounterNote,
    string? OrderId,
    string? BundleId);

public sealed record LabEventResponse(
    string? Name,
    string? SubType,
    string? Status,
    string? Result);

internal sealed class ListPatientCareAuth : IAuth<ListPatientCareQuery, PatientCareResponse>
{
    public ListPatientCareAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

internal sealed class
    ListPatientCareHandler : IRequestHandler<ListPatientCareQuery, PatientCareResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPatientCareHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PatientCareResponse> Handle(ListPatientCareQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var careEvents = await GetCareEvents(session, query.PatientId, cancellationToken);
        var careTeams = new List<CareTeamResponse>();

        var eventsGroups = careEvents
            .Where(x => x.Practitioner != null)
            .OrderByDescending(x => x.Date)
            .GroupBy(x => x.Practitioner!.Id);

        foreach (var eventGroup in eventsGroups) 
        {
            var lastEvent = eventGroup.First();
            var practitioner = lastEvent.Practitioner;

            string? order = null;
            string? orderResult = null;
            if (lastEvent.Medication != null)
            {
                order = lastEvent.Medication.Name;
            }
            else if (lastEvent.Lab != null)
            {
                order = lastEvent.Lab.Name;
            }

            careTeams.Add(new CareTeamResponse(practitioner!.SpecialtyClassification, practitioner!.Color, practitioner!.Name, string.Empty,
                lastEvent.Date, order, orderResult));
        }


        List<Domain.Note> notes = await session
            .Query<Domain.Note>()
            .Include(x => x.PractitionerId)
            .Include(x => x.EncounterId)
            .Where(x => x.EncounterId != query.EncounterId && x.IsLatest)
            .OrderByDescending(x => x.SignedAt)
            .ToListAsync(token: cancellationToken);

        List<CarePlanResponse> carePlans = [];

        foreach (var note in notes)
        {
            var practitioner = await session.LoadAsync<Domain.Employee>(note.PractitionerId, cancellationToken);
            var encounter = await session.LoadAsync<Domain.Encounter>(note.EncounterId, cancellationToken);

            carePlans.Add(new CarePlanResponse(
                note.Id,
                note.SignedAt,
                note.IsCompleted,
                practitioner!.ShortName,
                note.Name
            ));
        }

        return new PatientCareResponse(careTeams, carePlans, careEvents);
    }

    private async Task<List<CareEventsResponse>> GetCareEvents(IAsyncDocumentSession session, string patientId, CancellationToken cancellationToken)
    {
        var entries = await session.Query<CareEvents_ByPatient.Entry, CareEvents_ByPatient>()
            .Include(x => x.Id)
            .Include(x => x.PractitionerId)
            .Where(x => x.PatientId == patientId)
            .ProjectInto<CareEvents_ByPatient.Entry>()
            .ToListAsync(cancellationToken);

        var practitionerIds = entries.Select(x => x.PractitionerId).ToList();

        var practitionersLocations = await session.Query<LocationEmployee>()
            .Where(x => x.EmployeeId.In(practitionerIds))
            .ToListAsync(cancellationToken);

        var conditionIds = entries.Where(x => x.EventType == "Problem").Select(x => x.Icd10Id);

        var icd10Codes = await session.LoadAsync<Domain.Icd10>(conditionIds, cancellationToken);

        var result = new List<CareEventsResponse>();

        foreach (var entry in entries) 
        {
            var practitioner = await session.LoadAsync<Domain.Employee>(entry.PractitionerId, cancellationToken);
            var locationPractitioner = practitionersLocations
                .Where(x => x.EmployeeId == entry.PractitionerId && x.LocationId == entry.LocationId)
                .FirstOrDefault();

            var eventPractitioner = practitioner != null && locationPractitioner != null ? new EventPractitionerResponse(practitioner.Id, practitioner.ShortName,
                locationPractitioner.CalendarColor, locationPractitioner.SpecialtyClassification ?? string.Empty, practitioner.PhoneNumber) : null;

            if (entry.EventType == "Encounter")
            {
                result.Add(new CareEventsResponse(entry.EventDate, entry.EventType, eventPractitioner, null, null, null));
            }
            if (entry.EventType == "Problem")
            {
                var patientProblem = await session.LoadAsync<PatientProblem>(entry.Id, cancellationToken);
                var condition = icd10Codes.Values.FirstOrDefault(x => x.Id == patientProblem.Condition);
                var problem = new ProblemEventResponse(condition?.DisplayName ?? patientProblem.Condition, patientProblem.ClinicalConditionStatus, 
                    patientProblem.ConditionVerificationStatus, patientProblem.DiagnosisDate, patientProblem.Abatement);
                result.Add(new CareEventsResponse(entry.EventDate, entry.EventType, eventPractitioner, problem, null, null));
            }
            if (entry.EventType == "Medication")
            {
                var medOrder = await session.LoadAsync<OrderMedication>(entry.Id, cancellationToken);
                var medication = new MedicationEventResponse(medOrder.Name, medOrder.Status,
                    medOrder.Frequency, medOrder.CustomFrequency, medOrder.Duration, medOrder.Prn, medOrder.PrnReason, null, null, medOrder.Id, null);
                result.Add(new CareEventsResponse(entry.EventDate, entry.EventType, eventPractitioner, null, medication, null));
            }
            if (entry.EventType == "Lab")
            {
                var labOrder = await session.LoadAsync<OrderLab>(entry.Id, cancellationToken);
                var lab = new LabEventResponse(labOrder.Name, "Lab", labOrder.Status, null);
                result.Add(new CareEventsResponse(entry.EventDate, entry.EventType, eventPractitioner, null, null, lab));
            }
            if (entry.EventType == "Procedure")
            {
                var procedureOrder = await session.LoadAsync<OrderProcedure>(entry.Id, cancellationToken);
                var procedure = new LabEventResponse(procedureOrder.Name, "Procedure", procedureOrder.Status, null);
                result.Add(new CareEventsResponse(entry.EventDate, "Lab", eventPractitioner, null, null, procedure));
            }

            if (entry.EventType == "Bundle")
            {
                var bundleOrder = await session.LoadAsync<OrderBundle>(entry.Id, cancellationToken);
                foreach (var item in bundleOrder.Orders)
                {
                    if (item is OrderMedication bundleMedOrder)
                    {
                        var medication = new MedicationEventResponse(bundleMedOrder.Name, bundleMedOrder.Status, bundleMedOrder.Frequency,
                            bundleMedOrder.CustomFrequency, bundleMedOrder.Duration, bundleMedOrder.Prn, bundleMedOrder.PrnReason, null, null, bundleMedOrder.Id, bundleOrder.Id);
                        result.Add(new CareEventsResponse(entry.EventDate, "Medication", eventPractitioner, null, medication, null));
                    }
                    if (item is OrderLab bundleLabOrder)
                    {
                        var lab = new LabEventResponse(bundleLabOrder.Name, "Lab", bundleLabOrder.Status, null);
                        result.Add(new CareEventsResponse(entry.EventDate, "Lab", eventPractitioner, null, null, lab));
                    }
                    if (item is OrderProcedure bundleProcedureOrder)
                    {
                        var procedure = new LabEventResponse(bundleProcedureOrder.Name, "Procedure", bundleProcedureOrder.Status, null);
                        result.Add(new CareEventsResponse(entry.EventDate, "Lab", eventPractitioner, null, null, procedure));
                    }
                }
            }
        }

        return result;
    }
}