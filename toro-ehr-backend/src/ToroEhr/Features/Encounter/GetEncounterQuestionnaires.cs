using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public record GetEncounterQuestionnairesQuery(string EncounterId)
    : AuthRequest<List<EncounterQuestionnaireResponse>>;

internal class
    GetEncounterQuestionnairesAuth : IAuth<GetEncounterQuestionnairesQuery, List<EncounterQuestionnaireResponse>>
{
    public GetEncounterQuestionnairesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class
    GetEncounterQuestionnairesHandler : IRequestHandler<GetEncounterQuestionnairesQuery,
    List<EncounterQuestionnaireResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetEncounterQuestionnairesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<EncounterQuestionnaireResponse>> Handle(GetEncounterQuestionnairesQuery query,
        CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        var encounter = await session.LoadAsync<Domain.Encounter>(query.EncounterId, cancellationToken);

        var questionnaireResponses = await session.Query<Domain.QuestionnaireResponse>()
            .Where(x => x.EncounterId == query.EncounterId ||
                        (x.EncounterId == null && x.PatientId == encounter.PatientId))
            .ToListAsync(token: cancellationToken);

        if (!questionnaireResponses.Any())
            return new List<EncounterQuestionnaireResponse>();

        // get all latest questionnaires in one query
        var questionnaireIds = questionnaireResponses.Select(x => x.QuestionnaireId).Distinct().ToList();
        var latestQuestionnaires = await session.Query<Domain.Questionnaire>()
            .Where(x => (x.Id.In(questionnaireIds) || x.QuestionnaireId.In(questionnaireIds)) && x.IsLatest)
            .ToListAsync(cancellationToken);

        var questionnaireLookup = latestQuestionnaires.ToDictionary(q => q.QuestionnaireId ?? q.Id);

        return questionnaireResponses
            .Where(qr => questionnaireLookup.ContainsKey(qr.QuestionnaireId))
            .Select(qr =>
            {
                var questionnaire = questionnaireLookup[qr.QuestionnaireId];

                return new EncounterQuestionnaireResponse(
                    questionnaire.Title,
                    questionnaire.LocationId,
                    questionnaire.Questions.Select(q =>
                    {
                        var questionAnswers = qr.Answers
                            .FirstOrDefault(a => a.QuestionId == q.Id)?.Values ?? [];

                        return new QuestionResponse(q.Text, questionAnswers);
                    }).ToList());
            })
            .ToList();
    }
}