using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public record GetEncounterQuestionnairesQuery(string EncounterId)
    : AuthRequest<List<EncounterQuestionnaireResponse>>;

internal class
    GetEncounterQuestionnairesAuth : IAuth<GetEncounterQuestionnairesQuery, List<EncounterQuestionnaireResponse>>
{
    public GetEncounterQuestionnairesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class
    GetEncounterQuestionnairesHandler : IRequestHandler<GetEncounterQuestionnairesQuery,
    List<EncounterQuestionnaireResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetEncounterQuestionnairesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<EncounterQuestionnaireResponse>> Handle(GetEncounterQuestionnairesQuery query,
        CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        var patientId = (await session.LoadAsync<Domain.Encounter>(query.EncounterId, cancellationToken)).PatientId;

        var questionnaireResponses = await session.Query<Domain.QuestionnaireResponse>()
            .Include(x => x.QuestionnaireId)
            .Where(x => x.EncounterId == query.EncounterId ||
                        (x.EncounterId == null && x.PatientId == patientId))
            .ToListAsync(token: cancellationToken);

        var questionnaireIds = questionnaireResponses
            .Select(x => x.QuestionnaireId)
            .Distinct()
            .ToList();

        var questionnaires = await session.Query<Domain.Questionnaire>()
            .Where(x => x.Id.In(questionnaireIds) || x.QuestionnaireId.In(questionnaireIds))
            .ToListAsync(cancellationToken);

        // ensure we're using latest versions for display
        var latestQuestionnaires = new Dictionary<string, Domain.Questionnaire>();
        foreach (var kvp in questionnaires)
        {
            var questionnaire = kvp;
            if (questionnaire != null)
            {
                if (!questionnaire.IsLatest)
                {
                    // try to get latest version
                    var latestVersion = await session.Query<Domain.Questionnaire>()
                        .Where(x => (x.QuestionnaireId == questionnaire.QuestionnaireId || x.Id == questionnaire.QuestionnaireId) && x.IsLatest)
                        .FirstOrDefaultAsync(cancellationToken);
                    latestQuestionnaires[kvp.Id] = latestVersion ?? questionnaire;
                }
                else
                {
                    latestQuestionnaires[kvp.Id] = questionnaire;
                }
            }
        }

        return questionnaireResponses
            .Select(qr =>
            {
                var questionnaire = latestQuestionnaires.FirstOrDefault(x => x.Key == qr.QuestionnaireId).Value;

                return new EncounterQuestionnaireResponse(
                    questionnaire.Title,
                    questionnaire.LocationId,
                    questionnaire.Questions.Select(q =>
                    {
                        var questionAnswers = qr.Answers
                            .FirstOrDefault(a => a.QuestionId == q.Id)?.Values ?? [];

                        return new QuestionResponse(q.Text, questionAnswers);
                    }).ToList());
            })
            .ToList();
    }
}